# Firefox Extension Setup Guide

## Issue Description

The NeoNix Wallet extension works fine in Chrome but keeps loading in Mozilla Firefox. This is due to a manifest version compatibility issue between Chrome and Firefox.

## Root Cause

The extension had multiple Firefox compatibility issues:

1. **Manifest Version Conflict**: The build system was creating a conflicted manifest that tried to load both:
   - `service_worker: "scripts/app-init.js"` (MV3 style)
   - `page: "background.html"` (MV2 style)

   However, the `app-init.js` file was not being generated for Firefox builds.

2. **Missing Content Security Policy**: Firefox requires explicit CSP configuration with `unsafe-eval` directive for the extension to work properly.

3. **Session Storage API Issues**: The code was trying to use `browser.storage.session` API which Firefox doesn't support, causing the extension to hang during initialization.

## Solution

The following fixes were implemented:

1. **Force MV2 for Firefox**: Use `ENABLE_MV3=false` environment variable to ensure Firefox uses Manifest V2
2. **Added Firefox-specific CSP**: Added `content_security_policy` with `unsafe-eval` directive to Firefox manifest
3. **Fixed Session Storage**: Added Firefox detection to skip unsupported `browser.storage.session` API calls

## Build Commands

### For Development (with live reload):
```bash
# Firefox-specific development build
yarn start:firefox

# Or manually:
ENABLE_MV3=false yarn build:dev dev --apply-lavamoat=false --snow=false
```

### For Production/Distribution:
```bash
# Firefox-specific production build
yarn dist:firefox

# Or manually:
ENABLE_MV3=false yarn build dist --apply-lavamoat=false --snow=false
```

## Loading in Firefox

1. Build the extension using the Firefox-specific commands above
2. Open Firefox and navigate to `about:debugging#addons`
3. Click "Load Temporary Add-On"
4. Navigate to `dist/firefox/manifest.json` and select it
5. The extension should load successfully

## Verification

After building, verify the Firefox build is correct:

```bash
# Run the validation script
yarn validate:firefox
```

This will check:
- Manifest version is 2 (not 3)
- Background configuration uses `page` (not `service_worker`)
- Content Security Policy includes required directives
- All required files exist
- Extension ID is properly set

Manual verification:
- Check `dist/firefox/manifest.json`
- Should have `"manifest_version": 2`
- Should have `"background": {"page": "background.html", "persistent": true}`
- Should NOT have `"service_worker"` in the background section

## Chrome Compatibility

Chrome builds can still use either MV2 or MV3:
- Default build uses MV3: `yarn start` or `yarn dist`
- MV2 build: `yarn start:mv2` or `yarn dist:mv2`

## Troubleshooting

If the extension still doesn't load in Firefox:
1. Check the browser console for errors
2. Verify the manifest.json format is correct
3. Ensure all referenced files exist in the dist/firefox directory
4. Try clearing Firefox's extension cache and reloading
