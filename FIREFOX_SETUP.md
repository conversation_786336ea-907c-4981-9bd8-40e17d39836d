# Firefox Extension Setup Guide

## Issue Description

The NeoNix Wallet extension works fine in Chrome but keeps loading in Mozilla Firefox.

## Root Cause

The extension had multiple Firefox compatibility issues:

1. **Build System Exclusion**: Firefox was explicitly excluded from MV3 builds, causing manifest conflicts where Firefox tried to load a service worker that didn't exist.

2. **Missing Content Security Policy**: Firefox MV3 builds were missing proper CSP configuration.

3. **Session Storage API Issues**: The code was trying to use `browser.storage.session` API in contexts where Firefox doesn't support it.

4. **Popup Configuration Issues**: Firefox-specific manifest overrides were causing conflicts with popup initialization.

## Solution

The following fixes were implemented:

1. **Unified MV3 Support**: Removed Firefox exclusion from MV3 builds - both Chrome and Firefox now use Manifest V3
2. **Added Firefox MV3 CSP**: Added proper `content_security_policy` configuration for Firefox MV3
3. **Fixed Session Storage**: Added Firefox detection to skip unsupported `browser.storage.session` API calls
4. **Fixed Popup Configuration**: Removed conflicting popup overrides in Firefox manifest

## Build Commands

### Unified Build (works for both Chrome and Firefox):
```bash
# Development build
yarn start

# Production build
yarn dist
```

### Legacy Firefox-specific builds (if needed):
```bash
# Firefox MV2 development build (legacy)
yarn start:firefox

# Firefox MV2 production build (legacy)
yarn dist:firefox
```

## Loading in Firefox

1. Build the extension using the Firefox-specific commands above
2. Open Firefox and navigate to `about:debugging#addons`
3. Click "Load Temporary Add-On"
4. Navigate to `dist/firefox/manifest.json` and select it
5. The extension should load successfully

## Verification

After building, verify the Firefox build is correct:

```bash
# Run the validation script
yarn validate:firefox
```

This will check:
- Manifest version is 3 (MV3)
- Background configuration uses `service_worker` (not `page`)
- Content Security Policy includes required directives (MV3 format)
- Service worker file exists
- Extension ID is properly set

Manual verification:
- Check `dist/firefox/manifest.json`
- Should have `"manifest_version": 3`
- Should have `"background": {"service_worker": "scripts/app-init.js"}`
- Should have `"content_security_policy": {"extension_pages": "..."}`

## Browser Compatibility

Both Chrome and Firefox now use the same MV3 build:
- Default build uses MV3 for both browsers: `yarn start` or `yarn dist`
- Legacy MV2 builds available if needed: `yarn start:mv2` or `yarn dist:mv2`

## Troubleshooting

If the extension still doesn't load in Firefox:
1. Check the browser console for errors
2. Verify the manifest.json format is correct
3. Ensure all referenced files exist in the dist/firefox directory
4. Try clearing Firefox's extension cache and reloading
