{"applications": {"gecko": {"id": "<EMAIL>", "strict_min_version": "115.0"}}, "content_security_policy": {"extension_pages": "script-src 'self' 'unsafe-eval' 'wasm-unsafe-eval'; object-src 'none'; frame-ancestors 'none'; font-src 'self';", "sandbox": "sandbox allow-scripts; script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none'; default-src 'none'; connect-src *; font-src 'self';"}, "action": {"default_icon": {"16": "images/icon-16.png", "19": "images/icon-19.png", "32": "images/icon-32.png", "38": "images/icon-38.png", "64": "images/icon-64.png", "128": "images/icon-128.png", "512": "images/icon-512.png"}, "default_title": "NeoNix Wallet"}, "manifest_version": 3}