<!DOCTYPE html>
<html dir="ltr">
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta charset="utf-8" />

    <% if (it.isTest) { %>
    <title>NeoNix Wallet Dialog</title>
    <% } else { %>
    <title>NeoNix Wallet</title>
    <% } %>

    <style>
      #app-content {
        display: flex;
        flex-flow: column;
      }

      #loading__logo {
        width: 10rem;
        height: 10rem;
        align-self: center;
        margin: 10rem 0 0 0;
      }

      #loading__spinner {
        width: 2rem;
        height: 2rem;
        align-self: center;
        margin-top: 1rem;
      }
    </style>
    <link rel="preload" href="/_locales/en/messages.json" as="fetch" crossorigin="anonymous" />
    <link rel="stylesheet" href="../ui/css/index.scss" />
  </head>
  <body class="notification">
    <div id="app-content">
      <img
        id="loading__logo"
        src="./images/logo/neonix_icon.png"
        alt=""
        loading="lazy"
      />
      <img
        id="loading__spinner"
        src="./images/spinner.gif"
        alt=""
        loading="lazy"
      />
    </div>
    <div id="popover-content"></div>
    <script src="./scripts/load/ui.ts" defer></script>
  </body>
</html>
