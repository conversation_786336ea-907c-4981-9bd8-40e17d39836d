{"name": "metamask-crx", "version": "12.18.3", "private": true, "repository": {"type": "git", "url": "https://github.com/NeoNix/neonix-wallet-extension.git"}, "scripts": {"webpack": "tsx ./development/webpack/launch.ts", "webpack:clearcache": "./development/clear-webpack-cache.js", "foundryup": "npx mm-foundryup", "postinstall": "yarn webpack:clearcache && yarn foundryup", "env:e2e": "SEGMENT_HOST='https://api.segment.io' SEGMENT_WRITE_KEY='FAKE' yarn", "start": "yarn build:dev dev --apply-lavamoat=false --snow=false", "start:with-state": "node ./app/scripts/start-with-wallet-state.mjs", "start:mv2": "ENABLE_MV3=false yarn build:dev dev --apply-lavamoat=false --snow=false", "start:firefox": "ENABLE_MV3=false yarn build:dev dev --apply-lavamoat=false --snow=false", "start:flask": "yarn start --build-type flask", "start:flask:mv2": "ENABLE_MV3=false yarn start --build-type flask --apply-lavamoat=false --snow=false", "start:lavamoat": "yarn build:dev dev --apply-lavamoat=true", "dist": "yarn build dist", "dist:mv2": "ENABLE_MV3=false yarn build dist", "dist:firefox": "ENABLE_MV3=false yarn build dist --apply-lavamoat=false --snow=false", "validate:firefox": "node scripts/validate-firefox-build.js", "build": "yarn lavamoat:build", "build:dev": "node development/build/index.js", "circular-deps:check": "tsx development/circular-deps.ts check", "circular-deps:update": "tsx development/circular-deps.ts update", "start:test": "yarn env:e2e build:dev testDev --apply-lavamoat=false", "start:test:flask": "yarn start:test --build-type flask --apply-lavamoat=false", "start:test:mv2:flask": "ENABLE_MV3=false yarn start:test:flask --apply-lavamoat=false --snow=false", "start:test:mv2": "ENABLE_MV3=false BLOCKAID_FILE_CDN=static.cx.metamask.io/api/v1/confirmations/ppom yarn start:test --apply-lavamoat=false --snow=false", "build:test": "yarn env:e2e build test", "build:test:dev": "yarn env:e2e build:dev testDev --apply-lavamoat=false", "build:test:flask": "yarn build:test --build-type flask", "build:test:flask:mv2": "ENABLE_MV3=false yarn build:test:flask", "build:test:mv2": "ENABLE_MV3=false BLOCKAID_FILE_CDN=static.cx.metamask.io/api/v1/confirmations/ppom yarn build:test", "build:test:webpack": "BLOCKAID_FILE_CDN=static.cx.metamask.io/api/v1/confirmations/ppom yarn env:e2e webpack --test --browser=chrome --browser=firefox --no-cache --zip --lockdown --sentry --snow", "test": "yarn lint && yarn test:unit", "dapp": "node development/static-server.js node_modules/@metamask/test-dapp/dist --port 8080", "dapp-multichain": "node development/static-server.js node_modules/@metamask/test-dapp-multichain/build --port 8080", "dapp-solana": "node development/static-server.js node_modules/@metamask/test-dapp-solana/dist --port 8080", "dapp-chain": "GANACHE_ARGS='-b 2' concurrently -k -n ganache,dapp -p '[{time}][{name}]' 'yarn ganache:start' 'sleep 5 && yarn dapp'", "forwarder": "node ./development/static-server.js ./node_modules/@metamask/forwarder/dist/ --port 9010", "dapp-forwarder": "concurrently -k -n forwarder,dapp -p '[{time}][{name}]' 'yarn forwarder' 'yarn dapp'", "test:unit": "jest", "anvil": "node_modules/.bin/anvil", "test:unit:watch": "jest --watch", "test:unit:coverage": "jest --coverage", "test:unit:webpack": "tsx --test development/webpack/test/*.test.ts", "test:unit:webpack:coverage": "nyc --reporter=html --reporter=json --reporter=text --report-dir=./coverage/webpack tsx --test development/webpack/test/*.test.ts", "test:integration": "npx webpack build --config ./development/webpack/webpack.integration.tests.config.ts && jest --config jest.integration.config.js", "test:integration:coverage": "yarn test:integration --coverage", "test:e2e:chrome": "SELENIUM_BROWSER=chrome tsx test/e2e/run-all.ts", "test:e2e:chrome:flask": "SELENIUM_BROWSER=chrome tsx test/e2e/run-all.ts --build-type flask", "test:e2e:chrome:webpack": "SELENIUM_BROWSER=chrome tsx test/e2e/run-all.ts", "test:api-specs": "SELENIUM_BROWSER=chrome tsx test/e2e/run-openrpc-api-test-coverage.ts", "test:api-specs-multichain": "SELENIUM_BROWSER=chrome ts-node test/e2e/run-api-specs-multichain.ts", "test:e2e:swap": "yarn playwright test --project=swap", "test:e2e:global": "yarn playwright test --project=global", "test:e2e:pw:report": "yarn playwright show-report public/playwright/playwright-reports/html", "test:e2e:chrome:rpc": "SELENIUM_BROWSER=chrome tsx test/e2e/run-all.ts --rpc", "test:e2e:chrome:multi-provider": "MULTIPROVIDER=true SELENIUM_BROWSER=chrome tsx test/e2e/run-all.ts --multi-provider", "test:e2e:firefox": "SELENIUM_BROWSER=firefox tsx test/e2e/run-all.ts", "test:e2e:firefox:flask": "SELENIUM_BROWSER=firefox tsx test/e2e/run-all.ts --build-type flask", "test:e2e:single": "node test/e2e/run-e2e-test.js", "ganache:start": "./development/run-ganache.sh", "sentry:publish": "node ./development/sentry-publish.js", "lint": "yarn lint:prettier && yarn lint:eslint && yarn lint:tsc && yarn lint:styles", "lint:fix": "yarn lint:prettier:fix && yarn lint:eslint:fix && yarn lint:styles:fix && yarn circular-deps:update", "lint:prettier": "prettier --check --cache -- '**/*.{json,md,mdx,yml}'", "lint:prettier:fix": "prettier --write --cache -- '**/*.{json,md,mdx,yml}'", "lint:changed": "./development/get-changed-file-names.sh | grep -E --regexp='[.](js|ts|tsx)$' | tr '\\n' '\\0' | xargs -0 eslint --cache --cache-location node_modules/.cache/eslint/.eslint-cache", "lint:changed:fix": "./development/get-changed-file-names.sh | grep -E --regexp='[.](js|ts|tsx)$' | tr '\\n' '\\0' | xargs -0 eslint --fix --cache --cache-location node_modules/.cache/eslint/.eslint-cache", "lint:changelog": "auto-changelog validate", "lint:changelog:rc": "auto-changelog validate --rc", "lint:debug": "DEBUG=eslint:cli-engine yarn lint", "lint:eslint": "eslint . --ext js,ts,tsx,snap --cache --cache-location node_modules/.cache/eslint/.eslint-cache", "lint:eslint:fix": "yarn lint:eslint --fix --cache --cache-location node_modules/.cache/eslint/.eslint-cache", "lint:lockfile:dedupe": "yarn dedupe --check", "lint:lockfile:dedupe:fix": "yarn dedupe", "lint:lockfile": "lockfile-lint --path yarn.lock --allowed-hosts npm yarn github.com codeload.github.com --empty-hostname true --allowed-schemes \"https:\" \"git+https:\" \"npm:\" \"patch:\" \"workspace:\"", "lint:shellcheck": "./development/shellcheck.sh", "lint:styles": "stylelint '*/**/*.scss'", "lint:styles:fix": "yarn lint:styles --fix", "lint:tsc": "tsc", "validate-source-maps": "node ./development/sourcemap-validator.js", "verify-locales": "node ./development/verify-locale-strings.js", "verify-locales:fix": "node ./development/verify-locale-strings.js --fix", "mozilla-lint": "addons-linter dist/firefox", "devtools:react": "react-devtools", "devtools:redux": "remotedev --hostname=localhost --port=8000", "start:dev": "concurrently -k -n build,react,redux yarn:start yarn:devtools:react yarn:devtools:redux", "announce": "node development/announcer.js", "storybook": "storybook dev -p 6006 -c .storybook", "storybook:build": "storybook build -c .storybook -o storybook-build", "storybook:deploy": "storybook-to-ghpages --existing-output-dir storybook-build --remote storybook --branch master", "update-changelog": "auto-changelog update", "generate:migration": "./development/generate-migration.sh", "lavamoat:build": "lavamoat development/build/index.js --policy lavamoat/build-system/policy.json --policyOverride lavamoat/build-system/policy-override.json", "lavamoat:build:auto": "yarn lavamoat:build --writeAutoPolicy", "lavamoat:debug:build": "yarn lavamoat:build --writeAutoPolicyDebug --policydebug lavamoat/build-system/policy-debug.json", "lavamoat:debug:webapp": "WRITE_AUTO_POLICY_DEBUG=1 yarn lavamoat:webapp:auto", "lavamoat:webapp:auto": "ENABLE_MV3=true node ./development/generate-lavamoat-policies.js --devMode=true", "lavamoat:webapp:auto:ci": "ENABLE_MV3=true node ./development/generate-lavamoat-policies.js --parallel=false", "lavamoat:auto": "yarn lavamoat:build:auto && yarn lavamoat:webapp:auto", "ts-migration:dashboard:build": "tsx development/ts-migration-dashboard/scripts/build-app.ts", "ts-migration:dashboard:deploy": "gh-pages --dist development/ts-migration-dashboard/build/final --remote ts-migration-dashboard", "ts-migration:dashboard:watch": "yarn ts-migration:dashboard:build --watch", "ts-migration:enumerate": "tsx development/ts-migration-dashboard/scripts/write-list-of-files-to-convert.ts", "test-storybook": "test-storybook -c .storybook", "test-storybook:ci": "concurrently -k -s first -n \"SB,TEST\" -c \"magenta,blue\" \"yarn storybook:build && npx http-server storybook-build --port 6006 \" \"wait-on tcp:6006 && echo 'Build done. Running storybook tests...' && yarn test-storybook --maxWorkers=2\"", "githooks:install": "husky install", "fitness-functions": "tsx development/fitness-functions/index.ts", "generate-beta-commit": "node ./development/generate-beta-commit.js", "validate-branch-name": "validate-branch-name", "add-release-label-to-pr-and-linked-issues": "ts-node ./.github/scripts/add-release-label-to-pr-and-linked-issues.ts", "check-pr-has-required-labels": "ts-node ./.github/scripts/check-pr-has-required-labels.ts", "close-release-bug-report-issue": "ts-node ./.github/scripts/close-release-bug-report-issue.ts", "check-template-and-add-labels": "ts-node ./.github/scripts/check-template-and-add-labels.ts", "create-bug-report-issue": "ts-node ./.github/scripts/create-bug-report-issue.ts", "audit": "yarn npm audit --recursive --environment production --severity moderate --ignore '@metamask/types (deprecation)'", "download-builds": "tsx .devcontainer/download-builds.ts prep-build", "download-builds:test": "tsx .devcontainer/download-builds.ts prep-build-test", "master-sync": "node development/master-sync.js", "update-mock-cdn": "node test/e2e/mock-cdn/update-mock-cdn-files.js", "update-snap-binary": "tsx test/e2e/mock-response-data/snaps/update-snap-binary.ts", "attributions:check": "./development/attributions-check.sh", "attributions:generate": "./development/generate-attributions.sh"}, "resolutions": {"base-x@^3.0.x": "^3.0.11", "base-x@3.0.9": "^3.0.11", "base-x@^5.0.x": "^5.0.1", "chokidar": "^3.6.0", "@types/react": "^17.0.11", "bn.js": "^5.2.1", "ganache/abstract-level": "1.0.4", "git-url-parse@^12.0.0": "^13.1.0", "glob-parent": "^6.0.2", "netmask": "^2.0.1", "js-sha3": "^0.9.2", "jsonschema": "^1.4.1", "ast-types": "^0.14.2", "acorn@^7.0.0": "patch:acorn@npm:7.4.1#.yarn/patches/acorn-npm-7.4.1-f450b4646c.patch", "acorn@^7.4.1": "patch:acorn@npm:7.4.1#.yarn/patches/acorn-npm-7.4.1-f450b4646c.patch", "acorn@^7.1.1": "patch:acorn@npm:7.4.1#.yarn/patches/acorn-npm-7.4.1-f450b4646c.patch", "acorn@7.4.1": "patch:acorn@npm:7.4.1#.yarn/patches/acorn-npm-7.4.1-f450b4646c.patch", "object.values@^1.1.5": "patch:object.values@npm%3A1.1.5#./.yarn/patches/object.values-npm-1.1.5-f1de7f3742.patch", "error@^7.0.0": "patch:error@npm%3A7.0.2#./.yarn/patches/error-npm-7.0.2-6dfbeab4da.patch", "eslint-import-resolver-typescript@^2.5.0": "patch:eslint-import-resolver-typescript@npm%3A2.5.0#./.yarn/patches/eslint-import-resolver-typescript-npm-2.5.0-3b8adf0d03.patch", "borc@^2.1.2": "patch:borc@npm%3A2.1.2#./.yarn/patches/borc-npm-2.1.2-8ffcc2dd81.patch", "convert-source-map@^1.7.0": "patch:convert-source-map@npm%3A1.1.3#./.yarn/patches/convert-source-map-npm-1.1.3-7f1bfeabd4.patch", "convert-source-map@1.7.0": "patch:convert-source-map@npm%3A1.1.3#./.yarn/patches/convert-source-map-npm-1.1.3-7f1bfeabd4.patch", "convert-source-map@^1.0.0": "patch:convert-source-map@npm%3A1.1.3#./.yarn/patches/convert-source-map-npm-1.1.3-7f1bfeabd4.patch", "convert-source-map@~1.1.0": "patch:convert-source-map@npm%3A1.1.3#./.yarn/patches/convert-source-map-npm-1.1.3-7f1bfeabd4.patch", "convert-source-map@^0.3.3": "patch:convert-source-map@npm%3A1.1.3#./.yarn/patches/convert-source-map-npm-1.1.3-7f1bfeabd4.patch", "convert-source-map@^1.5.0": "patch:convert-source-map@npm%3A1.1.3#./.yarn/patches/convert-source-map-npm-1.1.3-7f1bfeabd4.patch", "convert-source-map@^1.6.0": "patch:convert-source-map@npm%3A1.1.3#./.yarn/patches/convert-source-map-npm-1.1.3-7f1bfeabd4.patch", "abort-controller@^3.0.0": "patch:abort-controller@npm%3A3.0.0#./.yarn/patches/abort-controller-npm-3.0.0-2f3a9a2bcb.patch", "await-semaphore@^0.1.3": "patch:await-semaphore@npm%3A0.1.3#./.yarn/patches/await-semaphore-npm-0.1.3-b7a0001fab.patch", "eslint@npm:^8.7.0": "patch:eslint@npm%3A8.57.0#~/.yarn/patches/eslint-npm-8.57.0-4286e12a3a.patch", "ethereumjs-util@^7.0.10": "patch:ethereumjs-util@npm%3A7.1.5#./.yarn/patches/ethereumjs-util-npm-7.1.5-5bb4d00000.patch", "gulp-sourcemaps@^3.0.0": "patch:gulp-sourcemaps@npm%3A3.0.0#./.yarn/patches/gulp-sourcemaps-npm-3.0.0-1ae0fbef6d.patch", "inline-source-map@~0.6.0": "patch:inline-source-map@npm%3A0.6.2#./.yarn/patches/inline-source-map-npm-0.6.2-96902459a0.patch", "plugin-error@^1.0.1": "patch:plugin-error@npm%3A1.0.1#./.yarn/patches/plugin-error-npm-1.0.1-7d15e880d6.patch", "plugin-error@1.0.1": "patch:plugin-error@npm%3A1.0.1#./.yarn/patches/plugin-error-npm-1.0.1-7d15e880d6.patch", "regenerator-runtime@^0.13.4": "patch:regenerator-runtime@npm%3A0.13.7#./.yarn/patches/regenerator-runtime-npm-0.13.7-41bcbe64ea.patch", "ws@8.13.0": "^8.17.1", "ws@7.4.6": "^7.5.10", "jsdom@^16.7.0": "patch:jsdom@npm%3A16.7.0#./.yarn/patches/jsdom-npm-16.7.0-216c5c4bf9.patch", "trim": "^0.0.3", "@eslint/eslintrc@npm:^2.1.4": "patch:@eslint/eslintrc@npm%3A2.1.4#~/.yarn/patches/@eslint-eslintrc-npm-2.1.4-1ff4b5f908.patch", "@fortawesome/fontawesome-free@^5.13.0": "patch:@fortawesome/fontawesome-free@npm%3A5.13.0#./.yarn/patches/@fortawesome-fontawesome-free-npm-5.13.0-f20fc0388d.patch", "fast-json-patch@^3.1.0": "patch:fast-json-patch@npm%3A3.1.1#./.yarn/patches/fast-json-patch-npm-3.1.1-7e8bb70a45.patch", "parse5@^7.0.0": "patch:parse5@npm%3A7.1.2#./.yarn/patches/parse5-npm-7.1.2-aa9a92c270.patch", "zxcvbn@^4.4.2": "patch:zxcvbn@npm%3A4.4.2#./.yarn/patches/zxcvbn-npm-4.4.2-6527983856.patch", "watchify@^4.0.0": "patch:watchify@npm%3A4.0.0#./.yarn/patches/watchify-npm-4.0.0-4fd965dd49.patch", "undeclared-identifiers@^1.1.2": "patch:undeclared-identifiers@npm%3A1.1.2#./.yarn/patches/undeclared-identifiers-npm-1.1.2-13d6792e9e.patch", "stylelint@^13.6.1": "patch:stylelint@npm%3A13.6.1#./.yarn/patches/stylelint-npm-13.6.1-47aaddf62b.patch", "symbol-observable": "^2.0.3", "async-done@~1.3.2": "patch:async-done@npm%3A1.3.2#./.yarn/patches/async-done-npm-1.3.2-1f0a4a8997.patch", "async-done@^1.2.0": "patch:async-done@npm%3A1.3.2#./.yarn/patches/async-done-npm-1.3.2-1f0a4a8997.patch", "async-done@^1.2.2": "patch:async-done@npm%3A1.3.2#./.yarn/patches/async-done-npm-1.3.2-1f0a4a8997.patch", "fast-json-patch@^3.1.1": "patch:fast-json-patch@npm%3A3.1.1#./.yarn/patches/fast-json-patch-npm-3.1.1-7e8bb70a45.patch", "@types/readable-stream-2@^2.3.15": "npm:@types/readable-stream@^2.3.15", "@types/readable-stream-3@^4.0.4": "npm:@types/readable-stream@^4.0.4", "readable-stream-2@^2.3.3": "npm:readable-stream@^2.3.3", "readable-stream-3@^3.6.2": "npm:readable-stream@^3.6.2", "semver@7.3.7": "^7.5.4", "semver@7.3.8": "^7.5.4", "lavamoat-core@npm:^16.2.2": "patch:lavamoat-core@npm%3A16.2.2#~/.yarn/patches/lavamoat-core-npm-16.2.2-e361ff1f8a.patch", "@metamask/snaps-sdk": "^8.0.0", "@swc/types@0.1.5": "^0.1.6", "@babel/core": "patch:@babel/core@npm%3A7.25.9#~/.yarn/patches/@babel-core-npm-7.25.9-4ae3bff7f3.patch", "@babel/runtime": "patch:@babel/runtime@npm%3A7.25.9#~/.yarn/patches/@babel-runtime-npm-7.25.9-fe8c62510a.patch", "@spruceid/siwe-parser@npm:2.1.0": "patch:@spruceid/siwe-parser@npm%3A2.1.0#~/.yarn/patches/@spruceid-siwe-parser-npm-2.1.0-060b7ede7a.patch", "ts-mixer@npm:^6.0.3": "patch:ts-mixer@npm%3A6.0.4#~/.yarn/patches/ts-mixer-npm-6.0.4-5d9747bdf5.patch", "@solana/web3.js/rpc-websockets": "^8.0.1", "@json-schema-spec/json-pointer@npm:^0.1.2": "patch:@json-schema-spec/json-pointer@npm%3A0.1.2#~/.yarn/patches/@json-schema-spec-json-pointer-npm-0.1.2-3d06119887.patch", "@json-schema-tools/reference-resolver@npm:^1.2.6": "patch:@json-schema-tools/reference-resolver@npm%3A1.2.6#~/.yarn/patches/@json-schema-tools-reference-resolver-npm-1.2.6-4e1497c16d.patch", "@json-schema-tools/reference-resolver@npm:1.2.4": "patch:@json-schema-tools/reference-resolver@npm%3A1.2.6#~/.yarn/patches/@json-schema-tools-reference-resolver-npm-1.2.6-4e1497c16d.patch", "@json-schema-tools/reference-resolver@npm:^1.2.4": "patch:@json-schema-tools/reference-resolver@npm%3A1.2.6#~/.yarn/patches/@json-schema-tools-reference-resolver-npm-1.2.6-4e1497c16d.patch", "@json-schema-tools/reference-resolver@npm:^1.2.1": "patch:@json-schema-tools/reference-resolver@npm%3A1.2.6#~/.yarn/patches/@json-schema-tools-reference-resolver-npm-1.2.6-4e1497c16d.patch", "path-to-regexp": "1.9.0", "@ledgerhq/cryptoassets-evm-signatures/axios": "^1.8.2", "@ledgerhq/domain-service/axios": "^1.8.2", "@ledgerhq/evm-tools/axios": "^1.8.2", "@ledgerhq/hw-app-eth/axios": "^1.8.2", "@ledgerhq/hw-app-eth@npm:^6.42.0": "patch:@ledgerhq/hw-app-eth@npm%3A6.42.2#~/.yarn/patches/@ledgerhq-hw-app-eth-npm-6.42.2-46a44bfbf5.patch", "@ledgerhq/evm-tools@npm:^1.3.0": "patch:@ledgerhq/evm-tools@npm%3A1.3.0#~/.yarn/patches/@ledgerhq-evm-tools-npm-1.3.0-57435278f6.patch", "cross-spawn@npm:^5.0.1": "^7.0.6", "@solana/web3.js@npm:^1.95.0": "^1.95.8", "secp256k1@npm:^4.0.0": "4.0.4", "secp256k1@npm:^4.0.1": "4.0.4", "secp256k1@npm:4.0.2": "4.0.4", "secp256k1@npm:4.0.3": "4.0.4", "tslib@npm:^2.0.0": "~2.6.0", "tslib@npm:^2.0.1": "~2.6.0", "tslib@npm:^2.0.3": "~2.6.0", "tslib@npm:^2.1.0": "~2.6.0", "tslib@npm:^2.3.0": "~2.6.0", "tslib@npm:^2.3.1": "~2.6.0", "tslib@npm:^2.4.0": "~2.6.0", "tslib@npm:^2.6.2": "~2.6.0", "@ethersproject/signing-key/elliptic": "^6.6.1", "gridplus-sdk/elliptic": "^6.6.1", "@ethereumjs/tx": "patch:@ethereumjs/tx@npm%3A5.4.0#~/.yarn/patches/@ethereumjs-tx-npm-5.4.0-0c4a0f973e.patch", "@keystonehq/metamask-airgapped-keyring": "patch:@keystonehq/metamask-airgapped-keyring@npm%3A0.15.2#~/.yarn/patches/@keystonehq-metamask-airgapped-keyring-npm-0.15.2-94dd4b40d7.patch", "@trezor/connect-web": "~9.4.7", "@metamask/network-controller@npm:^23.3.0": "patch:@metamask/network-controller@npm%3A23.6.0#~/.yarn/patches/@metamask-network-controller-npm-23.6.0-9e4332461a.patch", "@metamask/network-controller@npm:^23.1.0": "patch:@metamask/network-controller@npm%3A23.6.0#~/.yarn/patches/@metamask-network-controller-npm-23.6.0-9e4332461a.patch", "nanoid@npm:^5.1.5": "^3.3.8", "tar-fs@npm:^2.1.0": "^2.1.3"}, "dependencies": {"@babel/runtime": "patch:@babel/runtime@npm%3A7.25.9#~/.yarn/patches/@babel-runtime-npm-7.25.9-fe8c62510a.patch", "@blockaid/ppom_release": "^1.5.3", "@ensdomains/content-hash": "^2.5.7", "@ethereumjs/tx": "patch:@ethereumjs/tx@npm%3A5.4.0#~/.yarn/patches/@ethereumjs-tx-npm-5.4.0-0c4a0f973e.patch", "@ethersproject/abi": "^5.6.4", "@ethersproject/bignumber": "^5.7.0", "@ethersproject/bytes": "^5.7.0", "@ethersproject/contracts": "^5.7.0", "@ethersproject/hash": "^5.7.0", "@ethersproject/hdnode": "^5.6.2", "@ethersproject/providers": "^5.7.2", "@ethersproject/wallet": "^5.7.0", "@fortawesome/fontawesome-free": "^5.13.0", "@keystonehq/bc-ur-registry-eth": "^0.21.0", "@keystonehq/metamask-airgapped-keyring": "patch:@keystonehq/metamask-airgapped-keyring@npm%3A0.15.2#~/.yarn/patches/@keystonehq-metamask-airgapped-keyring-npm-0.15.2-94dd4b40d7.patch", "@lavamoat/lavadome-react": "0.0.20", "@lavamoat/snow": "^2.0.3", "@material-ui/core": "^4.12.4", "@metamask/abi-utils": "^2.0.2", "@metamask/account-tree-controller": "^0.1.0", "@metamask/account-watcher": "^4.1.3", "@metamask/accounts-controller": "^30.0.0", "@metamask/address-book-controller": "^6.0.3", "@metamask/announcement-controller": "^7.0.3", "@metamask/approval-controller": "^7.0.0", "@metamask/assets-controllers": "patch:@metamask/assets-controllers@npm%3A68.0.0#~/.yarn/patches/@metamask-assets-controllers-npm-68.0.0-02447bcea7.patch", "@metamask/base-controller": "^8.0.0", "@metamask/bitcoin-wallet-snap": "^0.14.1", "@metamask/bridge-controller": "^32.1.1", "@metamask/bridge-status-controller": "29.0.0", "@metamask/browser-passworder": "^4.3.0", "@metamask/chain-agnostic-permission": "^0.7.0", "@metamask/contract-metadata": "^2.5.0", "@metamask/controller-utils": "^11.9.0", "@metamask/delegation-controller": "^0.4.0", "@metamask/delegation-deployments": "^0.11.0", "@metamask/design-tokens": "^7.1.0", "@metamask/ens-controller": "^15.0.2", "@metamask/ens-resolver-snap": "^0.1.2", "@metamask/eth-json-rpc-filters": "^9.0.0", "@metamask/eth-json-rpc-middleware": "^17.0.0", "@metamask/eth-ledger-bridge-keyring": "^11.0.3", "@metamask/eth-sig-util": "^7.0.1", "@metamask/eth-snap-keyring": "^13.0.0", "@metamask/eth-token-tracker": "^10.0.2", "@metamask/eth-trezor-keyring": "^8.0.0", "@metamask/etherscan-link": "^3.0.0", "@metamask/gas-fee-controller": "^23.0.0", "@metamask/institutional-wallet-snap": "1.3.1", "@metamask/jazzicon": "^2.0.0", "@metamask/json-rpc-engine": "^10.0.0", "@metamask/json-rpc-middleware-stream": "^8.0.4", "@metamask/kernel-browser-runtime": "^0.1.0", "@metamask/kernel-shims": "^0.2.0", "@metamask/kernel-utils": "^0.1.0", "@metamask/keyring-api": "^18.0.0", "@metamask/keyring-controller": "^22.0.2", "@metamask/keyring-internal-api": "^6.2.0", "@metamask/keyring-internal-snap-client": "^4.1.0", "@metamask/keyring-snap-client": "^5.0.0", "@metamask/logger": "^0.2.0", "@metamask/logging-controller": "^6.0.4", "@metamask/logo": "^4.0.0", "@metamask/message-manager": "^12.0.1", "@metamask/message-signing-snap": "1.1.2", "@metamask/metamask-eth-abis": "^3.1.1", "@metamask/multichain-api-client": "^0.6.2", "@metamask/multichain-api-middleware": "0.4.0", "@metamask/multichain-network-controller": "^0.8.0", "@metamask/multichain-transactions-controller": "^0.9.0", "@metamask/name-controller": "^8.0.3", "@metamask/network-controller": "patch:@metamask/network-controller@npm%3A23.6.0#~/.yarn/patches/@metamask-network-controller-npm-23.6.0-9e4332461a.patch", "@metamask/notification-services-controller": "^10.0.0", "@metamask/object-multiplex": "^2.0.0", "@metamask/obs-store": "^9.0.0", "@metamask/permission-controller": "^11.0.6", "@metamask/permission-log-controller": "^3.0.3", "@metamask/phishing-controller": "^12.3.2", "@metamask/post-message-stream": "^10.0.0", "@metamask/ppom-validator": "0.36.0", "@metamask/preinstalled-example-snap": "^0.4.0", "@metamask/profile-sync-controller": "^17.1.0", "@metamask/providers": "^22.1.0", "@metamask/rate-limit-controller": "^6.0.3", "@metamask/remote-feature-flag-controller": "^1.6.0", "@metamask/rpc-errors": "^7.0.0", "@metamask/safe-event-emitter": "^3.1.1", "@metamask/scure-bip39": "^2.0.3", "@metamask/selected-network-controller": "^22.1.0", "@metamask/signature-controller": "^30.0.0", "@metamask/smart-transactions-controller": "^16.3.1", "@metamask/snaps-controllers": "^13.0.0", "@metamask/snaps-execution-environments": "^9.0.0", "@metamask/snaps-rpc-methods": "^13.0.0", "@metamask/snaps-sdk": "^8.0.0", "@metamask/snaps-utils": "^10.0.0", "@metamask/solana-wallet-snap": "^1.33.1", "@metamask/solana-wallet-standard": "^0.5.0", "@metamask/streams": "^0.2.0", "@metamask/transaction-controller": "~57.1.0", "@metamask/user-operation-controller": "^36.0.0", "@metamask/utils": "^11.1.0", "@ngraveio/bc-ur": "^1.1.13", "@noble/hashes": "^1.3.3", "@popperjs/core": "^2.4.0", "@reduxjs/toolkit": "patch:@reduxjs/toolkit@npm%3A1.9.7#~/.yarn/patches/@reduxjs-toolkit-npm-1.9.7-b14925495c.patch", "@segment/loosely-validate-event": "^2.0.0", "@sentry/browser": "patch:@sentry/browser@npm%3A8.33.1#~/.yarn/patches/@sentry-browser-npm-8.33.1-4405cafca3.patch", "@sentry/types": "^8.33.1", "@sentry/utils": "^8.33.1", "@solana/addresses": "2.0.0-rc.4", "@swc/core": "1.4.11", "@trezor/connect-web": "~9.4.7", "@zxing/browser": "^0.1.5", "@zxing/library": "0.21.3", "await-semaphore": "^0.1.3", "base32-encode": "^1.2.0", "base64-js": "^1.5.1", "bignumber.js": "^4.1.0", "bitcoin-address-validation": "^2.2.3", "blo": "1.2.0", "bn.js": "^5.2.1", "bowser": "^2.11.0", "chart.js": "^4.4.1", "classnames": "^2.2.6", "cockatiel": "^3.1.2", "copy-to-clipboard": "^3.3.3", "cron-parser": "^4.5.0", "currency-formatter": "^1.4.2", "deep-freeze-strict": "1.1.1", "dompurify": "patch:dompurify@npm%3A3.2.5#~/.yarn/patches/dompurify-npm-3.2.5-d9af707abe.patch", "eth-chainlist": "~0.0.498", "eth-ens-namehash": "^2.0.8", "eth-lattice-keyring": "patch:eth-lattice-keyring@npm%3A0.12.4#~/.yarn/patches/eth-lattice-keyring-npm-0.12.4-c5fb3fcf54.patch", "eth-method-registry": "^4.0.0", "ethereumjs-util": "^7.0.10", "extension-port-stream": "^3.0.0", "fast-json-patch": "^3.1.1", "fuse.js": "^3.2.0", "he": "^1.2.0", "history": "^5.3.0", "human-standard-token-abi": "^2.0.0", "humanize-duration": "^3.32.1", "immer": "^9.0.6", "is-retry-allowed": "^2.2.0", "jest-junit": "^14.0.1", "labeled-stream-splicer": "^2.0.2", "localforage": "^1.9.0", "lodash": "^4.17.21", "loglevel": "^1.8.1", "lottie-web": "^5.12.2", "luxon": "^3.2.1", "nanoid": "^3.3.8", "pify": "^5.0.0", "prop-types": "^15.6.1", "punycode": "^2.1.1", "qrcode-generator": "1.4.4", "qrcode.react": "^4.2.0", "react": "^17.0.2", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-dom": "^17.0.2", "react-focus-lock": "^2.9.4", "react-idle-timer": "4.5.6", "react-markdown": "^6.0.3", "react-popper": "^2.2.3", "react-redux": "^7.2.9", "react-responsive-carousel": "^3.2.21", "react-router-dom": "^5.3.4", "react-router-dom-v5-compat": "^6.26.2", "react-simple-file-input": "^2.0.0", "react-tippy": "^1.2.2", "react-toggle-button": "^2.2.0", "react-transition-group": "^4.4.5", "readable-stream": "^3.6.2", "redux": "^4.0.5", "redux-thunk": "^2.3.0", "remove-trailing-slash": "^0.1.1", "reselect": "^5.1.1", "ses": "^1.1.0", "simple-git": "^3.20.0", "single-call-balance-checker-abi": "^1.0.0", "ts-mixer": "patch:ts-mixer@npm%3A6.0.4#~/.yarn/patches/ts-mixer-npm-6.0.4-5d9747bdf5.patch", "tslib": "~2.6.0", "unicode-confusables": "^0.1.1", "uri-js": "^4.4.1", "uuid": "^8.3.2", "xml2js": "^0.6.2", "zxcvbn": "^4.4.2"}, "devDependencies": {"@actions/core": "^1.10.0", "@actions/github": "^5.1.1", "@babel/code-frame": "^7.25.9", "@babel/core": "patch:@babel/core@npm%3A7.25.9#~/.yarn/patches/@babel-core-npm-7.25.9-4ae3bff7f3.patch", "@babel/eslint-parser": "^7.25.9", "@babel/eslint-plugin": "^7.25.9", "@babel/plugin-transform-logical-assignment-operators": "^7.25.9", "@babel/preset-env": "^7.25.9", "@babel/preset-react": "^7.25.9", "@babel/preset-typescript": "^7.25.9", "@babel/register": "^7.25.9", "@jest/globals": "^29.7.0", "@lavamoat/allow-scripts": "^3.3.1", "@lavamoat/lavadome-core": "0.0.20", "@lavamoat/lavapack": "^7.0.5", "@lydell/node-pty": "^1.1.0", "@metamask/api-specs": "^0.13.0", "@metamask/auto-changelog": "^2.1.0", "@metamask/build-utils": "^3.0.0", "@metamask/eslint-config": "^9.0.0", "@metamask/eslint-config-jest": "^9.0.0", "@metamask/eslint-config-mocha": "^9.0.0", "@metamask/eslint-config-nodejs": "^9.0.0", "@metamask/eslint-config-typescript": "^9.0.1", "@metamask/eslint-plugin-design-tokens": "^1.1.0", "@metamask/eth-json-rpc-provider": "^4.1.6", "@metamask/forwarder": "^1.1.0", "@metamask/foundryup": "^1.0.0", "@metamask/phishing-warning": "^5.0.0", "@metamask/preferences-controller": "^17.0.0", "@metamask/superstruct": "^3.2.1", "@metamask/test-bundler": "^1.0.0", "@metamask/test-dapp": "9.3.0", "@metamask/test-dapp-multichain": "^0.17.0", "@metamask/test-dapp-solana": "^0.3.1", "@octokit/core": "^3.6.0", "@octokit/types": "^14.0.0", "@open-rpc/meta-schema": "^1.14.6", "@open-rpc/mock-server": "^1.7.5", "@open-rpc/schema-utils-js": "^2.0.5", "@open-rpc/test-coverage": "^2.2.4", "@playwright/test": "^1.39.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.11", "@sentry/cli": "^2.19.4", "@slack/types": "^2.14.0", "@slack/webhook": "^7.0.5", "@storybook/addon-a11y": "^7.6.20", "@storybook/addon-actions": "^7.6.20", "@storybook/addon-designs": "^7.0.9", "@storybook/addon-docs": "^7.6.20", "@storybook/addon-essentials": "^7.6.20", "@storybook/addon-mdx-gfm": "^7.6.20", "@storybook/addons": "^7.6.20", "@storybook/api": "^7.6.20", "@storybook/client-api": "^7.6.20", "@storybook/components": "^7.6.20", "@storybook/react": "^7.6.20", "@storybook/react-webpack5": "^7.6.20", "@storybook/storybook-deployer": "^2.8.16", "@storybook/test-runner": "^0.14.1", "@storybook/theming": "^7.6.20", "@swc/helpers": "^0.5.7", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^12.1.5", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.6.1", "@tsconfig/node22": "^22.0.0", "@types/babelify": "^7.3.7", "@types/browserify": "^12.0.37", "@types/chrome": "^0.0.268", "@types/currency-formatter": "^1.5.1", "@types/fs-extra": "^9.0.13", "@types/gulp": "^4.0.9", "@types/gulp-autoprefixer": "^0.0.33", "@types/gulp-sass": "^5.0.0", "@types/gulp-sourcemaps": "^0.0.35", "@types/he": "^1", "@types/humanize-duration": "^3.27.4", "@types/jest": "^29.5.12", "@types/luxon": "^3.4.2", "@types/madge": "^5.0.3", "@types/micromatch": "^4", "@types/mocha": "^10.0.3", "@types/node": "^20", "@types/path-browserify": "^1.0.2", "@types/pify": "^5.0.1", "@types/react": "^17.0.11", "@types/react-beautiful-dnd": "^13", "@types/react-dom": "^17.0.11", "@types/react-redux": "^7.1.25", "@types/react-router-dom": "^5.3.3", "@types/readable-stream": "4.0.4", "@types/readable-stream-2": "^2.3.15", "@types/readable-stream-3": "^4.0.4", "@types/redux-mock-store": "1.0.6", "@types/remote-redux-devtools": "^0.5.5", "@types/selenium-webdriver": "^4.1.28", "@types/serve-handler": "^6.1.4", "@types/sinon": "^10.0.13", "@types/sprintf-js": "^1", "@types/unzipper": "^0.10.10", "@types/w3c-web-hid": "^1.0.3", "@types/watchify": "^3.11.1", "@types/webextension-polyfill": "^0.10.4", "@types/ws": "^8.5.10", "@types/xml2js": "^0", "@types/yargs": "^17.0.32", "@types/yargs-parser": "^21.0.3", "@types/zxcvbn": "^4.4.5", "@typescript-eslint/eslint-plugin": "^7.10.0", "@typescript-eslint/parser": "^7.10.0", "@viem/anvil": "^0.0.10", "@welldone-software/why-did-you-render": "^8.0.3", "@whitespace/storybook-addon-html": "^5.1.6", "addons-linter": "^6.28.0", "autoprefixer": "^10.4.19", "axios": "^1.8.2", "babelify": "^10.0.0", "bify-module-groups": "^2.0.0", "browserify": "^17.0.0", "browserslist": "^4.23.0", "bs58": "^6.0.0", "buffer": "^6.0.3", "chalk": "^4.1.2", "chokidar": "^3.6.0", "concurrently": "^8.2.2", "copy-webpack-plugin": "^12.0.2", "core-js-pure": "^3.38.0", "crypto-browserify": "^3.12.0", "css-loader": "^6.10.0", "css-to-xpath": "^0.1.0", "csstype": "^3.0.11", "del": "^6.1.1", "depcheck": "^1.4.3", "detect-port": "^1.5.1", "dotenv": "^16.4.5", "duplexify": "^4.1.1", "eslint": "patch:eslint@npm%3A8.57.0#~/.yarn/patches/eslint-npm-8.57.0-4286e12a3a.patch", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-node": "^0.3.4", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^26.6.0", "eslint-plugin-jsdoc": "41.1.2", "eslint-plugin-mocha": "^10.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.23.1", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-storybook": "^0.6.15", "eta": "^3.2.0", "ethers": "5.7.0", "fake-indexeddb": "^6.0.0", "fancy-log": "^1.3.3", "fast-glob": "^3.2.2", "fflate": "^0.8.2", "fs-extra": "^8.1.0", "ganache": "patch:ganache@npm%3A7.9.2#~/.yarn/patches/ganache-npm-7.9.2-a70dc8da34.patch", "gh-pages": "^5.0.0", "globby": "^11.0.4", "gulp": "^4.0.2", "gulp-autoprefixer": "^8.0.0", "gulp-livereload": "4.0.0", "gulp-postcss": "^9.0.1", "gulp-sass": "^5.1.0", "gulp-sort": "^2.0.0", "gulp-sourcemaps": "^3.0.0", "gulp-stylelint": "^13.0.0", "gulp-watch": "^5.0.1", "gulp-zip": "^5.1.0", "html-bundler-webpack-plugin": "^4.4.1", "https-browserify": "^1.0.0", "husky": "^8.0.3", "ini": "^3.0.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.3.1", "jest-environment-jsdom": "patch:jest-environment-jsdom@npm%3A29.7.0#~/.yarn/patches/jest-environment-jsdom-npm-29.7.0-0b72dd0e0b.patch", "jest-fixed-jsdom": "^0.0.9", "jest-preview": "^0.3.1", "jsdom": "^16.7.0", "json-schema-to-ts": "^3.0.1", "koa": "^2.7.0", "lavamoat": "^9.0.5", "lavamoat-browserify": "^18.1.2", "lavamoat-viz": "^7.0.5", "level": "^8.0.1", "lockfile-lint": "^4.10.6", "loose-envify": "^1.4.0", "madge": "^8.0.0", "micromatch": "^4.0.8", "mini-css-extract-plugin": "^2.9.1", "mocha": "^10.2.0", "mocha-junit-reporter": "^2.2.1", "mockttp": "^3.10.1", "navigator.locks": "^0.8.6", "nock": "patch:nock@npm%3A13.5.4#~/.yarn/patches/nock-npm-13.5.4-2c4f77b249.patch", "node-fetch": "^2.6.1", "nyc": "^15.1.0", "octokit": "^4.1.3", "path-browserify": "^1.0.1", "postcss": "^8.4.32", "postcss-discard-font-face": "^3.0.0", "postcss-loader": "^8.1.1", "postcss-rtlcss": "^4.0.9", "prettier": "^2.7.1", "prettier-eslint": "^15.0.1", "prettier-plugin-sort-json": "^1.0.0", "process": "^0.11.10", "pumpify": "^2.0.1", "randomcolor": "^0.5.4", "react-devtools": "^4.11.0", "react-syntax-highlighter": "^15.5.0", "read-installed": "^4.0.3", "readable-stream-2": "^2.3.3", "readable-stream-3": "^3.6.2", "redux-mock-store": "^1.5.4", "remote-redux-devtools": "^0.5.16", "resolve-url-loader": "^3.1.5", "sass-embedded": "^1.71.0", "sass-loader": "^14.1.1", "schema-utils": "^4.2.0", "selenium-webdriver": "^4.31.0", "semver": "^7.5.4", "serve-handler": "^6.1.2", "sinon": "^9.0.0", "source-map": "^0.7.4", "source-map-explorer": "^2.4.2", "sprintf-js": "^1.1.3", "storybook": "^7.6.20", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "string.prototype.matchall": "^4.0.2", "style-loader": "^0.21.0", "stylelint": "^13.6.1", "terser": "^5.7.0", "terser-webpack-plugin": "^5.3.10", "through2": "^4.0.2", "ts-node": "^10.9.2", "tsx": "^4.19.2", "tweetnacl": "^1.0.3", "typescript": "~5.4.5", "unzipper": "^0.12.3", "viem": "^2.21.8", "vinyl": "^2.2.1", "vinyl-buffer": "^1.0.1", "vinyl-source-stream": "^2.0.0", "vinyl-sourcemaps-apply": "^0.2.1", "wait-on": "^7.0.1", "watchify": "^4.0.0", "webextension-polyfill": "^0.8.0", "webpack": "^5.96.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.3", "ws": "^8.17.1", "yaml": "^2.4.1", "yargs": "^17.7.2", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=20.12.0 <20.15.0 || >=20.17.0", "yarn": "^4.9.1"}, "lavamoat": {"allowScripts": {"@sentry/cli": true, "react-devtools>electron": true, "@eth-optimism/contracts>@ethersproject/hardware-wallets>@ledgerhq/hw-transport-node-hid>@ledgerhq/hw-transport-node-hid-noevents>node-hid": false, "@eth-optimism/contracts>@ethersproject/hardware-wallets>@ledgerhq/hw-transport-node-hid>node-hid": false, "@eth-optimism/contracts>@ethersproject/hardware-wallets>@ledgerhq/hw-transport-node-hid>usb": false, "@metamask/controllers>web3-provider-engine>ethereumjs-util>keccak": false, "@metamask/controllers>web3-provider-engine>ethereumjs-util>secp256k1": false, "@metamask/controllers>web3-provider-engine>ethereumjs-vm>merkle-patricia-tree>ethereumjs-util>keccak": false, "@metamask/controllers>web3-provider-engine>ethereumjs-vm>merkle-patricia-tree>ethereumjs-util>secp256k1": false, "@metamask/eth-ledger-bridge-keyring>hdkey>secp256k1": false, "@storybook/api>core-js": false, "@storybook/core>@storybook/core-client>@storybook/ui>core-js-pure": false, "@storybook/test-runner>@storybook/core-common>esbuild": false, "eth-json-rpc-filters>eth-json-rpc-middleware>ethereumjs-util>keccak": false, "eth-json-rpc-filters>eth-json-rpc-middleware>ethereumjs-util>secp256k1": false, "eth-lattice-keyring>gridplus-sdk": false, "ethereumjs-util>ethereum-cryptography>keccak": false, "ganache>@trufflesuite/bigint-buffer": false, "ganache>@trufflesuite/uws-js-unofficial>bufferutil": false, "ganache>@trufflesuite/uws-js-unofficial>utf-8-validate": false, "ganache>bufferutil": false, "ganache>keccak": false, "ganache>leveldown": false, "ganache>secp256k1": false, "ganache>utf-8-validate": false, "ethereumjs-util>ethereum-cryptography>secp256k1": false, "gulp-watch>chokidar>fsevents": false, "gulp>glob-watcher>chokidar>fsevents": false, "webpack>watchpack>watchpack-chokidar2>chokidar>fsevents": false, "@keystonehq/bc-ur-registry-eth>hdkey>secp256k1": false, "eth-lattice-keyring>gridplus-sdk>secp256k1": false, "eth-lattice-keyring>secp256k1": false, "@storybook/react>@pmmmwh/react-refresh-webpack-plugin>core-js-pure": false, "@testing-library/jest-dom>aria-query>@babel/runtime-corejs3>core-js-pure": false, "web3": false, "web3>web3-bzz": false, "web3>web3-core>web3-core-requestmanager>web3-providers-ws>websocket>bufferutil": false, "web3>web3-core>web3-core-requestmanager>web3-providers-ws>websocket>es5-ext": false, "web3>web3-core>web3-core-requestmanager>web3-providers-ws>websocket>utf-8-validate": false, "web3>web3-shh": false, "@metamask/base-controller>simple-git-hooks": false, "@storybook/core>@storybook/core-server>webpack>watchpack>watchpack-chokidar2>chokidar>fsevents": false, "resolve-url-loader>es6-iterator>es5-ext": false, "@storybook/test-runner>playwright": true, "@storybook/addon-knobs>core-js": false, "@storybook/manager-webpack5>core-js": false, "@storybook/react-webpack5>@storybook/builder-webpack5>@swc/core": false, "@storybook/react-webpack5>@storybook/preset-react-webpack>@pmmmwh/react-refresh-webpack-plugin>core-js-pure": false, "ts-node>@swc/core": false, "jsdom>ws>bufferutil": false, "jsdom>ws>utf-8-validate": false, "@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs": false, "@trezor/connect-web>@trezor/connect>@trezor/transport>usb": false, "@trezor/connect-web>@trezor/connect>@trezor/blockchain-link>@solana/web3.js>bigint-buffer": false, "@trezor/connect-web>@trezor/connect>@trezor/utxo-lib>blake-hash": false, "@trezor/connect-web>@trezor/connect>@trezor/utxo-lib>tiny-secp256k1": false, "@storybook/test-runner>@swc/core": false, "@lavamoat/lavadome-react>@lavamoat/preinstall-always-fail": false, "ws>bufferutil": false, "ws>utf-8-validate": false, "@metamask/eth-trezor-keyring>@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs": false, "firebase>@firebase/firestore>@grpc/proto-loader>protobufjs": false, "@lavamoat/lavadome-core>@lavamoat/preinstall-always-fail": false, "tsx>esbuild": true, "@pmmmwh/react-refresh-webpack-plugin>core-js-pure": true, "@swc/core": true, "@lydell/node-pty": true, "$root$": true, "core-js-pure": true, "resolve-url-loader>es6-iterator>d>es5-ext": false, "resolve-url-loader>es6-iterator>d>es5-ext>esniff>es5-ext": false, "level>classic-level": false, "jest-preview": false, "@metamask/solana-wallet-snap>@solana/web3.js>bigint-buffer": false, "@metamask/test-dapp-multichain>react-scripts>react-app-polyfill>core-js": false, "@lavamoat/allow-scripts>@lavamoat/preinstall-always-fail": false, "@metamask/eth-trezor-keyring>@trezor/connect-web>@trezor/connect>@trezor/blockchain-link>@solana/web3.js>bigint-buffer": false, "@metamask/eth-trezor-keyring>@trezor/connect-web>@trezor/connect>@trezor/transport>usb": false, "@metamask/test-dapp-solana>@solana/spl-token>@solana/buffer-layout-utils>bigint-buffer": false, "@testing-library/dom>aria-query>@babel/runtime-corejs3>core-js-pure": false, "@metamask/kernel-browser-runtime>@metamask/streams": false, "@metamask/kernel-browser-runtime>@metamask/kernel-store>better-sqlite3": false, "@metamask/logger>@metamask/streams": false, "@metamask/streams": false}}, "packageManager": "yarn@4.9.1", "foundryup": {"binaries": ["anvil"], "checksums": {"algorithm": "sha256", "binaries": {"anvil": {"darwin-amd64": "8404e555223fe884557d5e22de494baf8b5f0b82c6f87a4c790c5150e546c9d0", "darwin-arm64": "888500bc210752e71a355ed4d492ad6dcb4c0ef54d283c105a29a5ccc73d0dbd", "linux-amd64": "6104069b183fa0f3cdcb692681da9dbd203a3c1bceb435853bbf7abd991c649e", "linux-arm64": "d66ed8f848e829882ebb65d28aaac72aeab6a101655bb62147186040655928b5", "win32-amd64": "6c71d9a7be39ed32b53c89bdbc83aa748f41587517212ffe2a8b955c3e9c2e9b"}}}, "version": "v0.3.0"}}