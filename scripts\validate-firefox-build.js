#!/usr/bin/env node

/**
 * Validation script for Firefox extension build
 * This script checks if the Firefox build is properly configured and all required files exist
 */

const fs = require('fs');
const path = require('path');

const FIREFOX_DIST_PATH = path.join(__dirname, '..', 'dist', 'firefox');
const MANIFEST_PATH = path.join(FIREFOX_DIST_PATH, 'manifest.json');

function validateFirefoxBuild() {
  console.log('🔍 Validating Firefox extension build...\n');

  // Check if Firefox dist directory exists
  if (!fs.existsSync(FIREFOX_DIST_PATH)) {
    console.error('❌ Firefox dist directory not found. Please run: yarn dist:firefox');
    process.exit(1);
  }

  // Check if manifest.json exists
  if (!fs.existsSync(MANIFEST_PATH)) {
    console.error('❌ manifest.json not found in Firefox dist directory');
    process.exit(1);
  }

  // Read and validate manifest
  let manifest;
  try {
    manifest = JSON.parse(fs.readFileSync(MANIFEST_PATH, 'utf8'));
  } catch (error) {
    console.error('❌ Failed to parse manifest.json:', error.message);
    process.exit(1);
  }

  // Validate manifest version
  if (manifest.manifest_version !== 2) {
    console.error(`❌ Expected manifest_version 2, got ${manifest.manifest_version}`);
    console.error('   Run: ENABLE_MV3=false yarn build dist --apply-lavamoat=false --snow=false');
    process.exit(1);
  }
  console.log('✅ Manifest version 2 (correct for Firefox)');

  // Validate background configuration
  if (!manifest.background) {
    console.error('❌ Background configuration missing from manifest');
    process.exit(1);
  }

  if (manifest.background.service_worker) {
    console.error('❌ Found service_worker in background config (MV3 style)');
    console.error('   This will cause loading issues in Firefox');
    console.error('   Run: ENABLE_MV3=false yarn build dist --apply-lavamoat=false --snow=false');
    process.exit(1);
  }

  if (manifest.background.page !== 'background.html') {
    console.error(`❌ Expected background.page to be 'background.html', got '${manifest.background.page}'`);
    process.exit(1);
  }

  if (!manifest.background.persistent) {
    console.error('❌ Background page should be persistent for Firefox');
    process.exit(1);
  }
  console.log('✅ Background configuration correct (MV2 style)');

  // Check if background.html exists
  const backgroundHtmlPath = path.join(FIREFOX_DIST_PATH, 'background.html');
  if (!fs.existsSync(backgroundHtmlPath)) {
    console.error('❌ background.html not found');
    process.exit(1);
  }
  console.log('✅ background.html exists');

  // Validate extension ID
  if (!manifest.applications?.gecko?.id) {
    console.error('❌ Firefox extension ID missing from manifest');
    process.exit(1);
  }
  console.log(`✅ Extension ID: ${manifest.applications.gecko.id}`);

  // Validate Content Security Policy
  if (!manifest.content_security_policy) {
    console.error('❌ Content Security Policy missing from manifest');
    console.error('   This will cause CSP errors in Firefox');
    process.exit(1);
  }

  if (!manifest.content_security_policy.includes('unsafe-eval')) {
    console.error('❌ CSP missing unsafe-eval directive');
    console.error('   This is required for Firefox compatibility');
    process.exit(1);
  }
  console.log('✅ Content Security Policy configured correctly');

  // Check critical script files
  const criticalScripts = [
    'scripts/sentry-install.js',
    'scripts/lockdown-install.js',
    'scripts/lockdown-run.js',
    'scripts/lockdown-more.js',
    'scripts/runtime-cjs.js'
  ];

  for (const script of criticalScripts) {
    const scriptPath = path.join(FIREFOX_DIST_PATH, script);
    if (!fs.existsSync(scriptPath)) {
      console.error(`❌ Critical script missing: ${script}`);
      process.exit(1);
    }
  }
  console.log('✅ All critical scripts exist');

  // Check background script chunks
  const backgroundScripts = [];
  const commonScripts = [];

  for (let i = 0; i < 20; i++) {
    const bgScript = path.join(FIREFOX_DIST_PATH, `background-${i}.js`);
    const commonScript = path.join(FIREFOX_DIST_PATH, `common-${i}.js`);

    if (fs.existsSync(bgScript)) backgroundScripts.push(`background-${i}.js`);
    if (fs.existsSync(commonScript)) commonScripts.push(`common-${i}.js`);
  }

  console.log(`✅ Found ${backgroundScripts.length} background script chunks`);
  console.log(`✅ Found ${commonScripts.length} common script chunks`);

  // Final success message
  console.log('\n🎉 Firefox extension build validation passed!');
  console.log('\n📋 Next steps:');
  console.log('1. Open Firefox and navigate to about:debugging#addons');
  console.log('2. Click "Load Temporary Add-On"');
  console.log(`3. Select: ${MANIFEST_PATH}`);
  console.log('4. The extension should load successfully');
}

if (require.main === module) {
  validateFirefoxBuild();
}

module.exports = { validateFirefoxBuild };
